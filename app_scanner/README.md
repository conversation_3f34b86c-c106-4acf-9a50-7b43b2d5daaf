# App Scanner

A Flutter application that scans mobile phones for apps not installed from Google Play Store, helping users identify potentially risky applications.

## Features

- **Comprehensive App Scanning**: Scans all installed applications on Android devices
- **Installation Source Detection**: Identifies whether apps were installed from:
  - Google Play Store
  - Other legitimate app stores (Samsung Galaxy Store, Amazon Appstore, etc.)
  - Sideloaded (manually installed APK files)
  - System apps
  - Unknown sources
- **Risk Assessment**: Categorizes apps by risk level (Low, Medium, High)
- **Detailed App Information**: Shows package names, versions, installation times, and installer details
- **Security Recommendations**: Provides actionable security advice for each app
- **Modern UI**: Clean, Material Design 3 interface with dark mode support

## Installation

### Prerequisites

- Flutter SDK (3.9.2 or higher)
- Android SDK (for building Android APK)
- Android device or emulator for testing

### Setup

1. Install dependencies:
```bash
flutter pub get
```

2. Run the app:
```bash
flutter run
```

### Building for Release

```bash
flutter build apk --release
```

## Permissions

The app requires the following Android permissions:

- `QUERY_ALL_PACKAGES`: To access the list of installed applications (Android 11+)

These permissions are automatically declared in the `AndroidManifest.xml` file.

## Architecture

### Core Components

1. **Models** (`lib/models/`)
   - `AppInfo`: Data model for application information
   - `AppSource`: Enumeration of installation sources

2. **Services** (`lib/services/`)
   - `AppScannerService`: Core scanning functionality
   - `AppVerificationService`: Risk assessment and source verification

3. **Screens** (`lib/screens/`)
   - `HomeScreen`: Main dashboard with security overview
   - `AppListScreen`: Filtered lists of applications
   - `AppDetailScreen`: Detailed information about individual apps

## Security Considerations

### What This App Detects

- Apps installed outside of Google Play Store
- Potentially risky sideloaded applications
- Apps from unknown or untrusted sources
- Installation patterns that may indicate security risks

### Limitations

- Cannot detect malicious apps that were installed through legitimate stores
- Does not perform real-time malware scanning
- Relies on installation source metadata which can be limited

### Recommendations

The app provides security recommendations such as:
- Reviewing apps from unknown sources
- Uninstalling suspicious applications
- Verifying app legitimacy through official stores
- Monitoring app permissions and behavior

## Development

### Running Tests

```bash
flutter test
```

### Code Analysis

```bash
flutter analyze
```

## Disclaimer

This app is for educational and security awareness purposes. Users should:
- Verify app legitimacy through multiple sources
- Use additional security tools for comprehensive protection
- Exercise caution when uninstalling system applications
- Understand that installation source alone doesn't guarantee app safety
