import '../models/app_info.dart';

class AppVerificationService {
  // Known installer package names for Google Play Store
  static const Set<String> _playStoreInstallers = {
    'com.android.vending',
    'com.google.android.packageinstaller',
    'com.android.packageinstaller', // Some devices
  };

  // Known installer package names for other legitimate app stores
  static const Map<String, String> _knownStores = {
    'com.amazon.venezia': 'Amazon Appstore',
    'com.sec.android.app.samsungapps': 'Samsung Galaxy Store',
    'com.huawei.appmarket': 'Huawei AppGallery',
    'com.xiaomi.market': 'Xiaomi GetApps',
    'com.oppo.market': 'OPPO App Market',
    'com.bbk.appstore': 'Vivo App Store',
    'com.vivo.appstore': 'Vivo App Store',
    'org.fdroid.fdroid': 'F-Droid',
    'com.aptoide.partners.xiaomi.store': 'Aptoide',
    'com.qihoo.appstore': '360 Mobile Assistant',
    'com.tencent.android.qqdownloader': 'Tencent MyApp',
    'com.baidu.appsearch': 'Baidu Mobile Assistant',
  };

  // System installer packages (usually for pre-installed apps)
  static const Set<String> _systemInstallers = {
    'android',
    'com.android.shell',
    'com.android.systemui',
    'com.google.android.gms',
    'com.google.android.gsf',
  };

  /// Verify if an app is from Google Play Store
  static bool isFromPlayStore(String installerPackageName) {
    return _playStoreInstallers.contains(installerPackageName);
  }

  /// Verify if an app is from a known legitimate app store
  static bool isFromKnownStore(String installerPackageName) {
    return _knownStores.containsKey(installerPackageName) || 
           _playStoreInstallers.contains(installerPackageName);
  }

  /// Get the name of the app store from installer package name
  static String getStoreName(String installerPackageName) {
    if (_playStoreInstallers.contains(installerPackageName)) {
      return 'Google Play Store';
    }
    return _knownStores[installerPackageName] ?? 'Unknown Store';
  }

  /// Determine the installation source of an app
  static AppSource determineAppSource(AppInfo app) {
    if (app.isSystemApp) {
      return AppSource.system;
    }

    final installer = app.installerPackageName;

    if (installer.isEmpty || installer == 'null') {
      return AppSource.sideloaded;
    }

    if (_playStoreInstallers.contains(installer)) {
      return AppSource.playStore;
    }

    if (_knownStores.containsKey(installer)) {
      return AppSource.otherStore;
    }

    if (_systemInstallers.contains(installer)) {
      return AppSource.system;
    }

    return AppSource.unknown;
  }

  /// Check if an app is potentially risky (sideloaded or unknown source)
  static bool isPotentiallyRisky(AppInfo app) {
    final source = determineAppSource(app);
    return source == AppSource.sideloaded || 
           source == AppSource.unknown ||
           (!app.isSystemApp && !isFromKnownStore(app.installerPackageName));
  }

  /// Get risk level description for an app
  static String getRiskDescription(AppInfo app) {
    final source = determineAppSource(app);
    
    switch (source) {
      case AppSource.playStore:
        return 'Low risk - Verified by Google Play Store';
      case AppSource.otherStore:
        return 'Medium risk - From known app store: ${getStoreName(app.installerPackageName)}';
      case AppSource.system:
        return 'Low risk - System application';
      case AppSource.sideloaded:
        return 'High risk - Manually installed (APK file)';
      case AppSource.unknown:
        return 'High risk - Unknown installation source';
    }
  }

  /// Categorize apps by risk level
  static Map<String, List<AppInfo>> categorizeAppsByRisk(List<AppInfo> apps) {
    final Map<String, List<AppInfo>> categorized = {
      'Low Risk': [],
      'Medium Risk': [],
      'High Risk': [],
    };

    for (final app in apps) {
      final source = determineAppSource(app);
      
      switch (source) {
        case AppSource.playStore:
        case AppSource.system:
          categorized['Low Risk']!.add(app);
          break;
        case AppSource.otherStore:
          categorized['Medium Risk']!.add(app);
          break;
        case AppSource.sideloaded:
        case AppSource.unknown:
          categorized['High Risk']!.add(app);
          break;
      }
    }

    return categorized;
  }

  /// Get statistics about app sources
  static Map<String, int> getSourceStatistics(List<AppInfo> apps) {
    final Map<String, int> stats = {};
    
    for (final app in apps) {
      final source = determineAppSource(app);
      final sourceName = source.displayName;
      stats[sourceName] = (stats[sourceName] ?? 0) + 1;
    }
    
    return stats;
  }

  /// Filter apps that are not from Play Store (excluding system apps)
  static List<AppInfo> getNonPlayStoreUserApps(List<AppInfo> apps) {
    return apps.where((app) => 
      !app.isSystemApp && 
      !isFromPlayStore(app.installerPackageName)
    ).toList();
  }

  /// Get apps that might need attention (high risk, non-system apps)
  static List<AppInfo> getAppsNeedingAttention(List<AppInfo> apps) {
    return apps.where((app) => 
      !app.isSystemApp && 
      isPotentiallyRisky(app)
    ).toList();
  }
}
