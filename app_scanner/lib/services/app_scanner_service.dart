import 'dart:io';
import 'package:device_apps/device_apps.dart';
import 'package:flutter/services.dart';
import '../models/app_info.dart';
import 'app_verification_service.dart';

class AppScannerService {
  static const MethodChannel _channel = MethodChannel('app_scanner/installer_info');

  /// Request necessary permissions for scanning apps
  Future<bool> requestPermissions() async {
    if (!Platform.isAndroid) {
      return false;
    }

    // For Android 11+ we need QUERY_ALL_PACKAGES permission
    // This is already declared in AndroidManifest.xml
    return true;
  }

  /// Scan all installed applications
  Future<List<AppInfo>> scanInstalledApps() async {
    if (!Platform.isAndroid) {
      throw UnsupportedError('App scanning is only supported on Android');
    }

    final hasPermission = await requestPermissions();
    if (!hasPermission) {
      throw Exception('Required permissions not granted');
    }

    try {
      // Get all installed applications including system apps
      final apps = await DeviceApps.getInstalledApplications(
        includeAppIcons: false,
        includeSystemApps: true,
        onlyAppsWithLaunchIntent: false,
      );

      final List<AppInfo> appInfoList = [];

      for (final app in apps) {
        try {
          final installerInfo = await _getInstallerInfo(app.packageName);
          final appInfo = AppInfo(
            packageName: app.packageName,
            appName: app.appName,
            version: app.versionName,
            isSystemApp: app.systemApp,
            isFromPlayStore: _isFromPlayStore(installerInfo),
            installerPackageName: installerInfo,
            installTime: _getInstallTime(app),
            updateTime: _getUpdateTime(app),
          );
          appInfoList.add(appInfo);
        } catch (e) {
          // Skip apps that can't be processed
          // print('Error processing app ${app.packageName}: $e');
          continue;
        }
      }

      return appInfoList;
    } catch (e) {
      throw Exception('Failed to scan installed apps: $e');
    }
  }

  /// Get apps that are NOT from Google Play Store
  Future<List<AppInfo>> getNonPlayStoreApps() async {
    final allApps = await scanInstalledApps();
    return allApps.where((app) => !app.isFromPlayStore && !app.isSystemApp).toList();
  }

  /// Get apps categorized by their installation source
  Future<Map<AppSource, List<AppInfo>>> getAppsBySource() async {
    final allApps = await scanInstalledApps();
    final Map<AppSource, List<AppInfo>> categorizedApps = {};

    for (final app in allApps) {
      final source = _determineAppSource(app);
      categorizedApps.putIfAbsent(source, () => []).add(app);
    }

    return categorizedApps;
  }

  /// Determine the installation source of an app
  AppSource _determineAppSource(AppInfo app) {
    return AppVerificationService.determineAppSource(app);
  }

  /// Get installer package name for an app
  Future<String> _getInstallerInfo(String packageName) async {
    try {
      // This would typically use platform channels to get installer info
      // For now, we'll use a simplified approach
      return await _getInstallerPackageName(packageName);
    } catch (e) {
      return '';
    }
  }

  /// Check if app is from Play Store based on installer
  bool _isFromPlayStore(String installerPackageName) {
    return AppVerificationService.isFromPlayStore(installerPackageName);
  }

  /// Get installer package name using platform channel
  Future<String> _getInstallerPackageName(String packageName) async {
    try {
      final String? installer = await _channel.invokeMethod('getInstallerPackageName', {
        'packageName': packageName,
      });
      return installer ?? '';
    } catch (e) {
      // print('Error getting installer for $packageName: $e');
      return '';
    }
  }

  /// Get install time (placeholder implementation)
  DateTime? _getInstallTime(Application app) {
    // This would require platform channel implementation
    return null;
  }

  /// Get update time (placeholder implementation)
  DateTime? _getUpdateTime(Application app) {
    // This would require platform channel implementation
    return null;
  }
}
