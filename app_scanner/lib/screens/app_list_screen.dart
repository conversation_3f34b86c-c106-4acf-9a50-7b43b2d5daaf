import 'package:flutter/material.dart';
import '../models/app_info.dart';
import '../services/app_verification_service.dart';
import 'app_detail_screen.dart';

class AppListScreen extends StatefulWidget {
  final String title;
  final List<AppInfo> apps;

  const AppListScreen({
    super.key,
    required this.title,
    required this.apps,
  });

  @override
  State<AppListScreen> createState() => _AppListScreenState();
}

class _AppListScreenState extends State<AppListScreen> {
  List<AppInfo> _filteredApps = [];
  String _searchQuery = '';
  String _sortBy = 'name'; // name, risk, source

  @override
  void initState() {
    super.initState();
    _filteredApps = List.from(widget.apps);
    _sortApps();
  }

  void _filterApps(String query) {
    setState(() {
      _searchQuery = query;
      if (query.isEmpty) {
        _filteredApps = List.from(widget.apps);
      } else {
        _filteredApps = widget.apps
            .where((app) =>
                app.appName.toLowerCase().contains(query.toLowerCase()) ||
                app.packageName.toLowerCase().contains(query.toLowerCase()))
            .toList();
      }
      _sortApps();
    });
  }

  void _sortApps() {
    setState(() {
      switch (_sortBy) {
        case 'name':
          _filteredApps.sort((a, b) => a.appName.compareTo(b.appName));
          break;
        case 'risk':
          _filteredApps.sort((a, b) {
            final aRisky = AppVerificationService.isPotentiallyRisky(a);
            final bRisky = AppVerificationService.isPotentiallyRisky(b);
            if (aRisky && !bRisky) return -1;
            if (!aRisky && bRisky) return 1;
            return a.appName.compareTo(b.appName);
          });
          break;
        case 'source':
          _filteredApps.sort((a, b) {
            final aSource = AppVerificationService.determineAppSource(a);
            final bSource = AppVerificationService.determineAppSource(b);
            final comparison = aSource.displayName.compareTo(bSource.displayName);
            return comparison != 0 ? comparison : a.appName.compareTo(b.appName);
          });
          break;
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(widget.title),
        bottom: PreferredSize(
          preferredSize: const Size.fromHeight(120),
          child: Column(
            children: [
              Padding(
                padding: const EdgeInsets.all(16),
                child: TextField(
                  decoration: const InputDecoration(
                    hintText: 'Search apps...',
                    prefixIcon: Icon(Icons.search),
                    border: OutlineInputBorder(),
                  ),
                  onChanged: _filterApps,
                ),
              ),
              Padding(
                padding: const EdgeInsets.symmetric(horizontal: 16),
                child: Row(
                  children: [
                    const Text('Sort by: '),
                    DropdownButton<String>(
                      value: _sortBy,
                      items: const [
                        DropdownMenuItem(value: 'name', child: Text('Name')),
                        DropdownMenuItem(value: 'risk', child: Text('Risk Level')),
                        DropdownMenuItem(value: 'source', child: Text('Source')),
                      ],
                      onChanged: (value) {
                        if (value != null) {
                          setState(() {
                            _sortBy = value;
                          });
                          _sortApps();
                        }
                      },
                    ),
                    const Spacer(),
                    Text('${_filteredApps.length} apps'),
                  ],
                ),
              ),
              const SizedBox(height: 8),
            ],
          ),
        ),
      ),
      body: _filteredApps.isEmpty
          ? Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  const Icon(Icons.search_off, size: 64, color: Colors.grey),
                  const SizedBox(height: 16),
                  Text(
                    _searchQuery.isEmpty ? 'No apps found' : 'No apps match your search',
                    style: Theme.of(context).textTheme.titleMedium,
                  ),
                ],
              ),
            )
          : ListView.builder(
              itemCount: _filteredApps.length,
              itemBuilder: (context, index) {
                final app = _filteredApps[index];
                return _buildAppListItem(app);
              },
            ),
    );
  }

  Widget _buildAppListItem(AppInfo app) {
    final source = AppVerificationService.determineAppSource(app);
    final isRisky = AppVerificationService.isPotentiallyRisky(app);

    return Card(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 4),
      child: ListTile(
        leading: CircleAvatar(
          backgroundColor: _getSourceColor(source),
          child: Icon(
            _getSourceIcon(source),
            color: Colors.white,
          ),
        ),
        title: Text(
          app.appName,
          style: const TextStyle(fontWeight: FontWeight.bold),
        ),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              app.packageName,
              style: const TextStyle(fontSize: 12, color: Colors.grey),
            ),
            const SizedBox(height: 4),
            Row(
              children: [
                Chip(
                  label: Text(
                    source.displayName,
                    style: const TextStyle(fontSize: 10),
                  ),
                  backgroundColor: _getSourceColor(source),
                  materialTapTargetSize: MaterialTapTargetSize.shrinkWrap,
                ),
                const SizedBox(width: 8),
                if (isRisky)
                  const Icon(
                    Icons.warning,
                    color: Colors.orange,
                    size: 16,
                  ),
              ],
            ),
          ],
        ),
        trailing: const Icon(Icons.chevron_right),
        onTap: () => _navigateToAppDetail(app),
      ),
    );
  }

  Color _getSourceColor(AppSource source) {
    switch (source) {
      case AppSource.playStore:
        return Colors.green;
      case AppSource.sideloaded:
        return Colors.red;
      case AppSource.otherStore:
        return Colors.orange;
      case AppSource.system:
        return Colors.blue;
      case AppSource.unknown:
        return Colors.grey;
    }
  }

  IconData _getSourceIcon(AppSource source) {
    switch (source) {
      case AppSource.playStore:
        return Icons.store;
      case AppSource.sideloaded:
        return Icons.download;
      case AppSource.otherStore:
        return Icons.storefront;
      case AppSource.system:
        return Icons.android;
      case AppSource.unknown:
        return Icons.help;
    }
  }

  void _navigateToAppDetail(AppInfo app) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => AppDetailScreen(app: app),
      ),
    );
  }
}
