import 'package:flutter/material.dart';
import '../models/app_info.dart';
import '../services/app_scanner_service.dart';
import '../services/app_verification_service.dart';
import 'app_list_screen.dart';

class HomeScreen extends StatefulWidget {
  const HomeScreen({super.key});

  @override
  State<HomeScreen> createState() => _HomeScreenState();
}

class _HomeScreenState extends State<HomeScreen> {
  final AppScannerService _scannerService = AppScannerService();
  bool _isScanning = false;
  List<AppInfo> _allApps = [];
  Map<String, int> _sourceStats = {};
  Map<String, List<AppInfo>> _riskCategories = {};

  @override
  void initState() {
    super.initState();
    _performInitialScan();
  }

  Future<void> _performInitialScan() async {
    await _scanApps();
  }

  Future<void> _scanApps() async {
    setState(() {
      _isScanning = true;
    });

    try {
      final apps = await _scannerService.scanInstalledApps();
      final stats = AppVerificationService.getSourceStatistics(apps);
      final riskCategories = AppVerificationService.categorizeAppsByRisk(apps);

      setState(() {
        _allApps = apps;
        _sourceStats = stats;
        _riskCategories = riskCategories;
        _isScanning = false;
      });
    } catch (e) {
      setState(() {
        _isScanning = false;
      });
      
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error scanning apps: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('App Scanner'),
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _isScanning ? null : _scanApps,
            tooltip: 'Refresh scan',
          ),
        ],
      ),
      body: _isScanning
          ? const Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  CircularProgressIndicator(),
                  SizedBox(height: 16),
                  Text('Scanning installed apps...'),
                ],
              ),
            )
          : _buildScanResults(),
    );
  }

  Widget _buildScanResults() {
    if (_allApps.isEmpty) {
      return const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.apps, size: 64, color: Colors.grey),
            SizedBox(height: 16),
            Text('No apps found'),
            SizedBox(height: 8),
            Text('Pull to refresh or check permissions'),
          ],
        ),
      );
    }

    final nonPlayStoreApps = AppVerificationService.getNonPlayStoreUserApps(_allApps);
    final riskyApps = AppVerificationService.getAppsNeedingAttention(_allApps);

    return RefreshIndicator(
      onRefresh: _scanApps,
      child: ListView(
        padding: const EdgeInsets.all(16),
        children: [
          _buildSummaryCard(nonPlayStoreApps.length, riskyApps.length),
          const SizedBox(height: 16),
          _buildQuickActions(nonPlayStoreApps, riskyApps),
          const SizedBox(height: 16),
          _buildSourceStatistics(),
          const SizedBox(height: 16),
          _buildRiskCategories(),
        ],
      ),
    );
  }

  Widget _buildSummaryCard(int nonPlayStoreCount, int riskyCount) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          children: [
          Row(
            children: [
              const Icon(Icons.security, size: 32),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Security Summary',
                      style: Theme.of(context).textTheme.titleLarge,
                    ),
                    const SizedBox(height: 8),
                    Text('Total apps: ${_allApps.length}'),
                    Text('Non-Play Store apps: $nonPlayStoreCount'),
                    Text(
                      'Apps needing attention: $riskyCount',
                      style: TextStyle(
                        color: riskyCount > 0 ? Colors.orange : Colors.green,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ]),
      ),
    );
  }

  Widget _buildQuickActions(List<AppInfo> nonPlayStoreApps, List<AppInfo> riskyApps) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          children: [
          Text(
            'Quick Actions',
            style: Theme.of(context).textTheme.titleMedium,
          ),
          const SizedBox(height: 12),
          Row(
            children: [
              Expanded(
                child: ElevatedButton.icon(
                  onPressed: () => _navigateToAppList(
                    'Non-Play Store Apps',
                    nonPlayStoreApps,
                  ),
                  icon: const Icon(Icons.warning),
                  label: Text('View ${nonPlayStoreApps.length} Non-Play Store'),
                ),
              ),
              const SizedBox(width: 8),
              Expanded(
                child: ElevatedButton.icon(
                  onPressed: () => _navigateToAppList(
                    'High Risk Apps',
                    riskyApps,
                  ),
                  icon: const Icon(Icons.error),
                  label: Text('View ${riskyApps.length} Risky'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.orange,
                    foregroundColor: Colors.white,
                  ),
                ),
              ),
            ],
          ),
        ]),
      ),
    );
  }

  Widget _buildSourceStatistics() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          children: [
          Text(
            'Installation Sources',
            style: Theme.of(context).textTheme.titleMedium,
          ),
          const SizedBox(height: 12),
          ..._sourceStats.entries.map((entry) => Padding(
                padding: const EdgeInsets.symmetric(vertical: 4),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(entry.key),
                    Chip(
                      label: Text(entry.value.toString()),
                      backgroundColor: _getSourceColor(entry.key),
                    ),
                  ],
                ),
              )),
        ]),
      ),
    );
  }

  Widget _buildRiskCategories() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          children: [
          Text(
            'Risk Categories',
            style: Theme.of(context).textTheme.titleMedium,
          ),
          const SizedBox(height: 12),
          ..._riskCategories.entries.map((entry) => ListTile(
                title: Text(entry.key),
                subtitle: Text('${entry.value.length} apps'),
                trailing: Icon(
                  _getRiskIcon(entry.key),
                  color: _getRiskColor(entry.key),
                ),
                onTap: () => _navigateToAppList(entry.key, entry.value),
              )),
        ]),
      ),
    );
  }

  Color _getSourceColor(String source) {
    switch (source) {
      case 'Google Play Store':
        return Colors.green.shade100;
      case 'Sideloaded':
        return Colors.red.shade100;
      case 'System App':
        return Colors.blue.shade100;
      default:
        return Colors.orange.shade100;
    }
  }

  IconData _getRiskIcon(String risk) {
    switch (risk) {
      case 'Low Risk':
        return Icons.check_circle;
      case 'Medium Risk':
        return Icons.warning;
      case 'High Risk':
        return Icons.error;
      default:
        return Icons.help;
    }
  }

  Color _getRiskColor(String risk) {
    switch (risk) {
      case 'Low Risk':
        return Colors.green;
      case 'Medium Risk':
        return Colors.orange;
      case 'High Risk':
        return Colors.red;
      default:
        return Colors.grey;
    }
  }

  void _navigateToAppList(String title, List<AppInfo> apps) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => AppListScreen(
          title: title,
          apps: apps,
        ),
      ),
    );
  }
}
