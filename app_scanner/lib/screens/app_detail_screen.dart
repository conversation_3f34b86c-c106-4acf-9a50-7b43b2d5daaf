import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../models/app_info.dart';
import '../services/app_verification_service.dart';

class AppDetailScreen extends StatelessWidget {
  final AppInfo app;

  const AppDetailScreen({super.key, required this.app});

  @override
  Widget build(BuildContext context) {
    final source = AppVerificationService.determineAppSource(app);
    final isRisky = AppVerificationService.isPotentiallyRisky(app);
    final riskDescription = AppVerificationService.getRiskDescription(app);
    final storeName = AppVerificationService.getStoreName(app.installerPackageName);

    return Scaffold(
      appBar: AppBar(
        title: Text(app.appName),
        actions: [
          IconButton(
            icon: const Icon(Icons.copy),
            onPressed: () => _copyPackageName(context),
            tooltip: 'Copy package name',
          ),
        ],
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildAppHeader(context, source, isRisky),
            const SizedBox(height: 24),
            _buildSecuritySection(context, source, riskDescription, storeName),
            const SizedBox(height: 24),
            _buildAppInfoSection(context),
            const SizedBox(height: 24),
            _buildTechnicalInfoSection(context),
            const SizedBox(height: 24),
            _buildRecommendationsSection(context, source, isRisky),
          ],
        ),
      ),
    );
  }

  Widget _buildAppHeader(BuildContext context, AppSource source, bool isRisky) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          children: [
          Row(
            children: [
              CircleAvatar(
                radius: 30,
                backgroundColor: _getSourceColor(source),
                child: Icon(
                  _getSourceIcon(source),
                  color: Colors.white,
                  size: 30,
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      app.appName,
                      style: Theme.of(context).textTheme.headlineSmall,
                    ),
                    const SizedBox(height: 4),
                    Text(
                      app.packageName,
                      style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                            color: Colors.grey[600],
                          ),
                    ),
                    const SizedBox(height: 8),
                    Row(
                      children: [
                        Chip(
                          label: Text(source.displayName),
                          backgroundColor: _getSourceColor(source),
                        ),
                        if (isRisky) ...[
                          const SizedBox(width: 8),
                          const Chip(
                            label: Text('Needs Attention'),
                            backgroundColor: Colors.orange,
                          ),
                        ],
                      ],
                    ),
                  ],
                ),
              ),
            ],
          ),
        ]),
      ),
    );
  }

  Widget _buildSecuritySection(
    BuildContext context,
    AppSource source,
    String riskDescription,
    String storeName,
  ) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          children: [
          Row(
            children: [
              Icon(
                Icons.security,
                color: Theme.of(context).primaryColor,
              ),
              const SizedBox(width: 8),
              Text(
                'Security Information',
                style: Theme.of(context).textTheme.titleMedium,
              ),
            ],
          ),
          const SizedBox(height: 16),
          _buildInfoRow('Installation Source', storeName),
          _buildInfoRow('Source Type', source.displayName),
          _buildInfoRow('Risk Assessment', riskDescription),
          if (app.installerPackageName.isNotEmpty)
            _buildInfoRow('Installer Package', app.installerPackageName),
        ]),
      ),
    );
  }

  Widget _buildAppInfoSection(BuildContext context) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          children: [
          Row(
            children: [
              Icon(
                Icons.info,
                color: Theme.of(context).primaryColor,
              ),
              const SizedBox(width: 8),
              Text(
                'App Information',
                style: Theme.of(context).textTheme.titleMedium,
              ),
            ],
          ),
          const SizedBox(height: 16),
          _buildInfoRow('App Name', app.appName),
          _buildInfoRow('Package Name', app.packageName),
          if (app.version != null)
            _buildInfoRow('Version', app.version!),
          _buildInfoRow('System App', app.isSystemApp ? 'Yes' : 'No'),
          _buildInfoRow('From Play Store', app.isFromPlayStore ? 'Yes' : 'No'),
        ]),
      ),
    );
  }

  Widget _buildTechnicalInfoSection(BuildContext context) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          children: [
          Row(
            children: [
              Icon(
                Icons.code,
                color: Theme.of(context).primaryColor,
              ),
              const SizedBox(width: 8),
              Text(
                'Technical Details',
                style: Theme.of(context).textTheme.titleMedium,
              ),
            ],
          ),
          const SizedBox(height: 16),
          if (app.installTime != null)
            _buildInfoRow('Install Time', _formatDateTime(app.installTime!)),
          if (app.updateTime != null)
            _buildInfoRow('Last Update', _formatDateTime(app.updateTime!)),
          _buildInfoRow('Installer Package', 
              app.installerPackageName.isEmpty ? 'Unknown' : app.installerPackageName),
        ]),
      ),
    );
  }

  Widget _buildRecommendationsSection(
    BuildContext context,
    AppSource source,
    bool isRisky,
  ) {
    final recommendations = _getRecommendations(source, isRisky);
    
    if (recommendations.isEmpty) return const SizedBox.shrink();

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          children: [
          Row(
            children: [
              Icon(
                Icons.lightbulb,
                color: Theme.of(context).primaryColor,
              ),
              const SizedBox(width: 8),
              Text(
                'Recommendations',
                style: Theme.of(context).textTheme.titleMedium,
              ),
            ],
          ),
          const SizedBox(height: 16),
          ...recommendations.map((recommendation) => Padding(
                padding: const EdgeInsets.only(bottom: 8),
                child: Row(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Icon(Icons.arrow_right, size: 16),
                    const SizedBox(width: 8),
                    Expanded(child: Text(recommendation)),
                  ],
                ),
              )),
        ]),
      ),
    );
  }

  Widget _buildInfoRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 120,
            child: Text(
              label,
              style: const TextStyle(fontWeight: FontWeight.bold),
            ),
          ),
          Expanded(
            child: Text(value),
          ),
        ],
      ),
    );
  }

  List<String> _getRecommendations(AppSource source, bool isRisky) {
    final recommendations = <String>[];

    switch (source) {
      case AppSource.sideloaded:
        recommendations.addAll([
          'This app was installed manually (sideloaded). Verify it came from a trusted source.',
          'Consider uninstalling if you don\'t remember installing it.',
          'Scan the APK file with antivirus software if available.',
        ]);
        break;
      case AppSource.unknown:
        recommendations.addAll([
          'The installation source is unknown. This could indicate a security risk.',
          'Review the app\'s permissions and behavior carefully.',
          'Consider uninstalling if the app is not essential.',
        ]);
        break;
      case AppSource.otherStore:
        recommendations.addAll([
          'This app was installed from an alternative app store.',
          'Ensure the store is reputable and the app is legitimate.',
          'Check for the same app on Google Play Store for better security.',
        ]);
        break;
      case AppSource.playStore:
        recommendations.add('This app was installed from Google Play Store and is generally safe.');
        break;
      case AppSource.system:
        recommendations.add('This is a system app that came pre-installed with your device.');
        break;
    }

    if (isRisky) {
      recommendations.add('Monitor this app\'s behavior and permissions regularly.');
    }

    return recommendations;
  }

  Color _getSourceColor(AppSource source) {
    switch (source) {
      case AppSource.playStore:
        return Colors.green;
      case AppSource.sideloaded:
        return Colors.red;
      case AppSource.otherStore:
        return Colors.orange;
      case AppSource.system:
        return Colors.blue;
      case AppSource.unknown:
        return Colors.grey;
    }
  }

  IconData _getSourceIcon(AppSource source) {
    switch (source) {
      case AppSource.playStore:
        return Icons.store;
      case AppSource.sideloaded:
        return Icons.download;
      case AppSource.otherStore:
        return Icons.storefront;
      case AppSource.system:
        return Icons.android;
      case AppSource.unknown:
        return Icons.help;
    }
  }

  String _formatDateTime(DateTime dateTime) {
    return '${dateTime.day}/${dateTime.month}/${dateTime.year} ${dateTime.hour}:${dateTime.minute.toString().padLeft(2, '0')}';
  }

  void _copyPackageName(BuildContext context) {
    Clipboard.setData(ClipboardData(text: app.packageName));
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Package name copied to clipboard'),
        duration: Duration(seconds: 2),
      ),
    );
  }
}
