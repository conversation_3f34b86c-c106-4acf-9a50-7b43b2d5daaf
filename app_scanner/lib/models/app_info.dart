class AppInfo {
  final String packageName;
  final String appName;
  final String? version;
  final bool isSystemApp;
  final bool isFromPlayStore;
  final String installerPackageName;
  final DateTime? installTime;
  final DateTime? updateTime;

  AppInfo({
    required this.packageName,
    required this.appName,
    this.version,
    required this.isSystemApp,
    required this.isFromPlayStore,
    required this.installerPackageName,
    this.installTime,
    this.updateTime,
  });

  @override
  String toString() {
    return 'AppInfo{packageName: $packageName, appName: $appName, isFromPlayStore: $isFromPlayStore, installer: $installerPackageName}';
  }

  Map<String, dynamic> toJson() {
    return {
      'packageName': packageName,
      'appName': appName,
      'version': version,
      'isSystemApp': isSystemApp,
      'isFromPlayStore': isFromPlayStore,
      'installerPackageName': installerPackageName,
      'installTime': installTime?.toIso8601String(),
      'updateTime': updateTime?.toIso8601String(),
    };
  }
}

enum AppSource {
  playStore,
  sideloaded,
  otherStore,
  system,
  unknown,
}

extension AppSourceExtension on AppSource {
  String get displayName {
    switch (this) {
      case AppSource.playStore:
        return 'Google Play Store';
      case AppSource.sideloaded:
        return 'Sideloaded';
      case AppSource.otherStore:
        return 'Other App Store';
      case AppSource.system:
        return 'System App';
      case AppSource.unknown:
        return 'Unknown';
    }
  }

  String get description {
    switch (this) {
      case AppSource.playStore:
        return 'Installed from Google Play Store';
      case AppSource.sideloaded:
        return 'Installed manually (APK file)';
      case AppSource.otherStore:
        return 'Installed from alternative app store';
      case AppSource.system:
        return 'Pre-installed system application';
      case AppSource.unknown:
        return 'Installation source unknown';
    }
  }
}
