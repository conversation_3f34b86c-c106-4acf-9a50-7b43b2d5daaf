package com.example.appscanner.app_scanner

import android.content.pm.PackageManager
import android.os.Build
import io.flutter.embedding.android.FlutterActivity
import io.flutter.embedding.engine.FlutterEngine
import io.flutter.plugin.common.MethodChannel

class MainActivity: FlutterActivity() {
    private val CHANNEL = "app_scanner/installer_info"

    override fun configureFlutterEngine(flutterEngine: FlutterEngine) {
        super.configureFlutterEngine(flutterEngine)

        MethodChannel(flutterEngine.dartExecutor.binaryMessenger, CHANNEL).setMethodCallHandler { call, result ->
            when (call.method) {
                "getInstallerPackageName" -> {
                    val packageName = call.argument<String>("packageName")
                    if (packageName != null) {
                        val installer = getInstallerPackageName(packageName)
                        result.success(installer)
                    } else {
                        result.error("INVALID_ARGUMENT", "Package name is required", null)
                    }
                }
                "getPackageInfo" -> {
                    val packageName = call.argument<String>("packageName")
                    if (packageName != null) {
                        val packageInfo = getPackageInfo(packageName)
                        result.success(packageInfo)
                    } else {
                        result.error("INVALID_ARGUMENT", "Package name is required", null)
                    }
                }
                else -> {
                    result.notImplemented()
                }
            }
        }
    }

    private fun getInstallerPackageName(packageName: String): String? {
        return try {
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.R) {
                // Android 11+
                val installSourceInfo = packageManager.getInstallSourceInfo(packageName)
                installSourceInfo.installingPackageName ?: installSourceInfo.initiatingPackageName
            } else {
                // Android 10 and below
                @Suppress("DEPRECATION")
                packageManager.getInstallerPackageName(packageName)
            }
        } catch (e: Exception) {
            null
        }
    }

    private fun getPackageInfo(packageName: String): Map<String, Any?>? {
        return try {
            val packageInfo = packageManager.getPackageInfo(packageName, 0)
            val applicationInfo = packageManager.getApplicationInfo(packageName, 0)

            mapOf(
                "packageName" to packageInfo.packageName,
                "versionName" to packageInfo.versionName,
                "versionCode" to if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.P) {
                    packageInfo.longVersionCode
                } else {
                    @Suppress("DEPRECATION")
                    packageInfo.versionCode.toLong()
                },
                "firstInstallTime" to packageInfo.firstInstallTime,
                "lastUpdateTime" to packageInfo.lastUpdateTime,
                "isSystemApp" to ((applicationInfo.flags and android.content.pm.ApplicationInfo.FLAG_SYSTEM) != 0),
                "installerPackageName" to getInstallerPackageName(packageName)
            )
        } catch (e: Exception) {
            null
        }
    }
}
